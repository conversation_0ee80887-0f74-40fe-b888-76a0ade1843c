# 🔁 Interactive Task Loop with User Feedback

This tool enables an AI-driven workflow where tasks are performed interactively in response to user input. After each task, the user is prompted for the next instruction. The loop continues until the user stops it manually or the maximum number of tool calls is reached.

---

## 🧠 How It Works

1. The AI performs its assigned task.
2. Upon completion, it runs a simple Python script to prompt the user for feedback or a new instruction.
3. Based on the user's input, the AI continues working.
4. The loop stops when:
   - The user manually stops, or
   - The tool call limit is reached.

---

## 📁 Files

### `userinput.py` (placed in the project root)

### `rules.md` ( copy the contents to your rules page)


